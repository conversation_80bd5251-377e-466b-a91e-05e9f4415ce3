# NextChat React 重构版本

这是 NextChat 项目的重构版本，从 Next.js 迁移到纯 React 18 + TypeScript + MobX + SCSS 的网页端应用。

## 🚀 技术栈

- **React 18** - 现代化的 React 框架
- **TypeScript 5.x** - 类型安全的 JavaScript
- **MobX 6** - 简单、可扩展的状态管理
- **SCSS Modules** - 模块化的样式管理
- **Vite** - 快速的构建工具
- **React Router DOM** - 客户端路由

## 📁 项目结构

```
src/
├── components/          # React 组件
│   ├── ui/             # 通用 UI 组件
│   ├── Chat.tsx        # 聊天界面
│   ├── Settings.tsx    # 设置页面
│   ├── Sidebar.tsx     # 侧边栏
│   └── ...
├── stores/             # MobX 状态管理
│   ├── ChatStore.ts    # 聊天状态
│   ├── ConfigStore.ts  # 配置状态
│   ├── PluginStore.ts  # 插件状态
│   └── index.ts        # Store 入口
├── styles/             # 全局样式
│   ├── globals.scss    # 全局样式
│   ├── variables.scss  # SCSS 变量
│   └── ...
├── icons/              # SVG 图标
├── App.tsx             # 应用入口
└── main.tsx            # React 入口
```

## 🔧 开发环境设置

### 安装依赖

```bash
npm install
# 或
yarn install
```

### 启动开发服务器

```bash
npm run dev
# 或
yarn dev
```

应用将在 http://localhost:3000 启动

### 构建生产版本

```bash
npm run build
# 或
yarn build
```

### 预览生产版本

```bash
npm run preview
# 或
yarn preview
```

## 📋 重构内容

### ✅ 已完成的重构

1. **技术栈迁移**
   - 从 Next.js 迁移到 Vite + React
   - 从 Zustand 迁移到 MobX
   - 保持 TypeScript 和 SCSS 支持

2. **功能简化**
   - 移除了 SD 绘图功能 (`/sd`, `/sd-new`)
   - 移除了 Masks 模板功能 (`/masks`)
   - 移除了身份验证功能 (`/auth`)
   - 移除了 Artifacts 功能 (`/artifacts/:id`)

3. **保留的核心功能**
   - ✅ 主聊天界面 (`/`, `/chat`)
   - ✅ 新建聊天 (`/new-chat`)
   - ✅ 系统设置 (`/settings`)
   - ✅ 插件管理 (`/plugins`)
   - ✅ 聊天搜索 (`/search-chat`)
   - ✅ MCP 市场 (`/mcp-market`)

4. **状态管理重构**
   - 使用 MobX 替代 Zustand
   - 重新设计 Store 结构
   - 保持数据持久化功能

5. **组件架构优化**
   - 模块化组件设计
   - SCSS Modules 样式隔离
   - TypeScript 类型安全

### 🔄 后端集成

重构后的应用期望后端提供以下 API：

```typescript
// POST /api/chat
interface ChatRequest {
  messages: ChatMessage[]
  model: string
}

interface ChatResponse {
  content: string
  model?: string
}
```

### 🎨 主题系统

支持自动、明亮、暗黑三种主题模式：
- 自动模式：跟随系统主题
- 明亮模式：浅色主题
- 暗黑模式：深色主题

### 📱 响应式设计

- 桌面端：完整的侧边栏 + 内容区布局
- 移动端：自适应布局，侧边栏可收起

## 🚧 待完善功能

1. **API 集成**
   - 需要实现后端 API 接口
   - 完善错误处理机制

2. **功能增强**
   - 消息流式渲染
   - 文件上传支持
   - 更多插件功能

3. **性能优化**
   - 虚拟滚动
   - 图片懒加载
   - 缓存策略

## 🔧 开发指南

### 添加新页面

1. 在 `src/components/` 创建组件文件
2. 在 `src/App.tsx` 添加路由
3. 如需状态管理，在相应 Store 中添加逻辑

### 添加新的状态

1. 在对应的 Store 文件中添加状态和方法
2. 使用 `makeAutoObservable` 使状态可观察
3. 在组件中使用 `observer` 包装组件

### 样式开发

1. 使用 SCSS Modules 进行样式隔离
2. 全局变量定义在 `src/styles/variables.scss`
3. 遵循现有的设计系统

## 📄 许可证

MIT License - 详见原项目许可证
