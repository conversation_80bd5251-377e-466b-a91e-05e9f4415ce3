{"name": "nextchat-react", "version": "2.15.8", "private": true, "type": "module", "license": "mit", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "mobx": "^6.10.2", "mobx-react-lite": "^4.0.5", "axios": "^1.7.5", "clsx": "^2.1.1", "nanoid": "^5.0.3", "lodash-es": "^4.17.21", "fuse.js": "^7.0.0", "react-markdown": "^8.0.7", "remark-breaks": "^3.0.2", "remark-gfm": "^3.0.1", "remark-math": "^5.1.1", "rehype-highlight": "^6.0.0", "rehype-katex": "^6.0.3", "markdown-to-txt": "^2.0.1", "emoji-picker-react": "^4.9.2", "use-debounce": "^9.0.4", "spark-md5": "^3.0.2", "html-to-image": "^1.11.11", "heic2any": "^0.0.4", "idb-keyval": "^6.2.1", "openapi-client-axios": "^7.5.5", "zod": "^3.24.1"}, "devDependencies": {"@types/react": "^18.2.70", "@types/react-dom": "^18.2.7", "@types/lodash-es": "^4.17.12", "@types/spark-md5": "^3.0.4", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "prettier": "^3.0.2", "sass": "^1.59.2", "typescript": "^5.2.2", "vite": "^4.4.5", "vite-plugin-svgr": "^3.2.0", "vitest": "^0.34.0"}}