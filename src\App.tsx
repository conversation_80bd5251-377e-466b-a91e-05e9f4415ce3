import { Routes, Route, Navigate } from 'react-router-dom'
import { observer } from 'mobx-react-lite'
import { useEffect } from 'react'
import { ErrorBoundary } from './components/ErrorBoundary'
import { Home } from './components/Home'
import { Chat } from './components/Chat'
import { NewChat } from './components/NewChat'
import { Settings } from './components/Settings'
import { PluginPage } from './components/Plugin'
import { SearchChat } from './components/SearchChat'
import { McpMarket } from './components/McpMarket'
import { useStores } from './stores'
import { useTheme } from './hooks'
import './App.scss'

const App = observer(() => {
  const { theme } = useTheme()

  useEffect(() => {
    // 初始化主题
    document.body.classList.remove('light', 'dark')
    if (theme === 'dark') {
      document.body.classList.add('dark')
    } else if (theme === 'light') {
      document.body.classList.add('light')
    }
  }, [theme])

  return (
    <ErrorBoundary>
      <div className="app">
        <Routes>
          <Route path="/" element={<Home />}>
            <Route index element={<Chat />} />
            <Route path="chat" element={<Chat />} />
            <Route path="new-chat" element={<NewChat />} />
            <Route path="settings" element={<Settings />} />
            <Route path="plugins" element={<PluginPage />} />
            <Route path="search-chat" element={<SearchChat />} />
            <Route path="mcp-market" element={<McpMarket />} />
          </Route>
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </ErrorBoundary>
  )
})

export default App
