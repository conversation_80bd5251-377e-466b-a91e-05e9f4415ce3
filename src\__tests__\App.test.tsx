import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import App from '../App'

// Mock the stores
vi.mock('../stores', () => ({
  useStores: () => ({
    configStore: {
      theme: 'light',
      config: {
        tightBorder: false,
      },
    },
  }),
}))

describe('App', () => {
  it('renders without crashing', () => {
    render(
      <BrowserRouter>
        <App />
      </BrowserRouter>
    )
    
    // Should render the main app container
    expect(document.querySelector('.app')).toBeInTheDocument()
  })
})
