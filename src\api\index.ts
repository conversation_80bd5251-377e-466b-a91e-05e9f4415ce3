import axios, { AxiosResponse } from 'axios'
import type { ChatMessage } from '../stores/ChatStore'

// 创建 axios 实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证 token
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`
    // }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response
  },
  (error) => {
    console.error('API Error:', error)
    
    if (error.response?.status === 401) {
      // 处理未授权错误
      // 可以跳转到登录页面或清除本地存储
    }
    
    return Promise.reject(error)
  }
)

// API 接口定义
export interface ChatRequest {
  messages: ChatMessage[]
  model: string
  temperature?: number
  max_tokens?: number
  stream?: boolean
}

export interface ChatResponse {
  content: string
  model?: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export interface StreamChatResponse {
  content: string
  done: boolean
}

// 聊天 API
export const chatApi = {
  // 发送聊天消息
  async sendMessage(request: ChatRequest): Promise<ChatResponse> {
    const response = await api.post<ChatResponse>('/chat', request)
    return response.data
  },

  // 流式聊天（如果后端支持）
  async sendMessageStream(
    request: ChatRequest,
    onMessage: (chunk: StreamChatResponse) => void
  ): Promise<void> {
    const response = await fetch('/api/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const reader = response.body?.getReader()
    if (!reader) {
      throw new Error('No response body')
    }

    const decoder = new TextDecoder()
    let buffer = ''

    try {
      while (true) {
        const { done, value } = await reader.read()
        
        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.trim() === '') continue
          
          try {
            const data = JSON.parse(line)
            onMessage(data)
          } catch (e) {
            console.warn('Failed to parse SSE data:', line)
          }
        }
      }
    } finally {
      reader.releaseLock()
    }
  },

  // 获取可用模型列表
  async getModels(): Promise<string[]> {
    const response = await api.get<string[]>('/models')
    return response.data
  },
}

// 插件 API
export const pluginApi = {
  // 获取插件列表
  async getPlugins(): Promise<any[]> {
    const response = await api.get('/plugins')
    return response.data
  },

  // 安装插件
  async installPlugin(pluginId: string): Promise<void> {
    await api.post(`/plugins/${pluginId}/install`)
  },

  // 卸载插件
  async uninstallPlugin(pluginId: string): Promise<void> {
    await api.delete(`/plugins/${pluginId}`)
  },
}

// 配置 API
export const configApi = {
  // 获取服务器配置
  async getServerConfig(): Promise<any> {
    const response = await api.get('/config')
    return response.data
  },

  // 更新配置
  async updateConfig(config: any): Promise<void> {
    await api.put('/config', config)
  },
}

export default api
