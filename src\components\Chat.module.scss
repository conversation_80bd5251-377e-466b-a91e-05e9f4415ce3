.chat {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background-color: var(--gray);
}

.chatHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: var(--border-in-light);
  background-color: var(--white);
  flex-shrink: 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chatHeaderTitle {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.chatHeaderMainTitle {
  font-size: 20px;
  font-weight: bold;
  color: var(--black);
}

.chatHeaderSubTitle {
  font-size: 12px;
  color: var(--black);
  opacity: 0.5;
}

.chatHeaderActions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.headerButton {
  min-width: 32px;
  min-height: 32px;
  padding: 6px;
  border-radius: 6px;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--hover-color);
    transform: scale(1.05);
  }
}

.chatBody {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: var(--gray);
  min-height: 0; // 确保 flex 子元素可以收缩
}

.chatMessageContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.chatWelcome {
  text-align: center;
  padding: 40px 20px;
}

.chatWelcomeTitle {
  font-size: 24px;
  font-weight: bold;
  color: var(--black);
  margin-bottom: 10px;
}

.chatWelcomeSubTitle {
  font-size: 14px;
  color: var(--black);
  opacity: 0.7;
}

.chatMessage {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 15px;
  border-radius: 10px;
  max-width: 70%;
  word-wrap: break-word;
  position: relative;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.messageContainer {
  position: relative;
}

.chatMessageUser {
  align-self: flex-end;
  background-color: var(--primary);
  color: white;
}

.chatMessageAssistant {
  align-self: flex-start;
  background-color: var(--white);
  color: var(--black);
  border: var(--border-in-light);
}

.chatMessageContent {
  line-height: 1.5;
  white-space: pre-wrap;
  position: relative;
}

.messageText {
  line-height: 1.6;
  white-space: pre-wrap;
  word-break: break-word;
}

.chatMessageStreaming {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chatMessageMeta {
  font-size: 11px;
  opacity: 0.5;
  align-self: flex-end;
}

.chatInputPanel {
  padding: 15px 20px;
  border-top: var(--border-in-light);
  background-color: var(--white);
  flex-shrink: 0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.chatInputContainer {
  display: flex;
  gap: 10px;
  align-items: flex-end;
  max-width: 800px;
  margin: 0 auto;
}

.chatInput {
  flex: 1;
  border: var(--border-in-light);
  border-radius: 10px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
  background-color: var(--white);
  color: var(--black);
  font-family: inherit;
  min-height: 20px;
  max-height: 200px;
  overflow-y: auto;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(29, 147, 171, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &::placeholder {
    color: var(--black);
    opacity: 0.5;
  }
}

.chatSendButton {
  flex-shrink: 0;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

// 消息操作按钮样式
.messageActions {
  opacity: 0;
  transition: opacity 0.2s ease;
}

.messageContainer:hover .messageActions {
  opacity: 1;
}

// 添加消息动画
.chatMessage {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .chatHeader {
    padding: 10px 15px;
  }

  .chatBody {
    padding: 15px;
  }

  .chatInputPanel {
    padding: 10px 15px;
  }

  .chatMessageContainer {
    max-width: 100%;
  }

  .chatMessage {
    max-width: 85%;
  }
}
