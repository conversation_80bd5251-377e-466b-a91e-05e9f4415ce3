.chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

.chatHeader {
  padding: 20px;
  border-bottom: var(--border-in-light);
  background-color: var(--white);
}

.chatHeaderTitle {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.chatHeaderMainTitle {
  font-size: 20px;
  font-weight: bold;
  color: var(--black);
}

.chatHeaderSubTitle {
  font-size: 12px;
  color: var(--black);
  opacity: 0.5;
}

.chatBody {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background-color: var(--gray);
}

.chatMessageContainer {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.chatWelcome {
  text-align: center;
  padding: 40px 20px;
}

.chatWelcomeTitle {
  font-size: 24px;
  font-weight: bold;
  color: var(--black);
  margin-bottom: 10px;
}

.chatWelcomeSubTitle {
  font-size: 14px;
  color: var(--black);
  opacity: 0.7;
}

.chatMessage {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 15px;
  border-radius: 10px;
  max-width: 70%;
  word-wrap: break-word;
}

.chatMessageUser {
  align-self: flex-end;
  background-color: var(--primary);
  color: white;
}

.chatMessageAssistant {
  align-self: flex-start;
  background-color: var(--white);
  color: var(--black);
  border: var(--border-in-light);
}

.chatMessageContent {
  line-height: 1.5;
  white-space: pre-wrap;
}

.chatMessageStreaming {
  display: flex;
  align-items: center;
  gap: 8px;
}

.chatMessageMeta {
  font-size: 11px;
  opacity: 0.5;
  align-self: flex-end;
}

.chatInputPanel {
  padding: 20px;
  border-top: var(--border-in-light);
  background-color: var(--white);
}

.chatInputContainer {
  display: flex;
  gap: 10px;
  align-items: flex-end;
  max-width: 800px;
  margin: 0 auto;
}

.chatInput {
  flex: 1;
  border: var(--border-in-light);
  border-radius: 10px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  outline: none;
  background-color: var(--white);
  color: var(--black);
  font-family: inherit;
  min-height: 20px;
  max-height: 200px;

  &:focus {
    border-color: var(--primary);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.chatSendButton {
  flex-shrink: 0;
}
