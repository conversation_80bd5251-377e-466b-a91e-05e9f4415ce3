import { observer } from 'mobx-react-lite'
import { useState, useRef, useEffect } from 'react'
import { useStores } from '../stores'
import { IconButton } from './ui/IconButton'
import styles from './Chat.module.scss'
import clsx from 'clsx'

// Placeholder icons - these would need to be imported from the actual icon files
const SendIcon = () => <span>→</span>
const LoadingIcon = () => <span>...</span>

export const Chat = observer(() => {
  const { chatStore } = useStores()
  const [userInput, setUserInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const currentSession = chatStore.currentSession
  const messages = currentSession.messages

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSubmit = async () => {
    if (!userInput.trim() || isLoading) return

    setIsLoading(true)
    try {
      await chatStore.onUserInput(userInput.trim())
      setUserInput('')
    } catch (error) {
      console.error('Failed to send message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className={styles.chat}>
      {/* Header */}
      <div className={styles.chatHeader}>
        <div className={styles.chatHeaderTitle}>
          <div className={styles.chatHeaderMainTitle}>NextChat</div>
          <div className={styles.chatHeaderSubTitle}>
            {messages.length} messages
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className={styles.chatBody}>
        <div className={styles.chatMessageContainer}>
          {messages.length === 0 ? (
            <div className={styles.chatWelcome}>
              <div className={styles.chatWelcomeTitle}>Welcome to NextChat</div>
              <div className={styles.chatWelcomeSubTitle}>
                Start a conversation with AI
              </div>
            </div>
          ) : (
            messages.map((message) => (
              <div
                key={message.id}
                className={clsx(styles.chatMessage, {
                  [styles.chatMessageUser]: message.role === 'user',
                  [styles.chatMessageAssistant]: message.role === 'assistant',
                })}
              >
                <div className={styles.chatMessageContent}>
                  {message.streaming ? (
                    <div className={styles.chatMessageStreaming}>
                      <LoadingIcon />
                      {message.content}
                    </div>
                  ) : (
                    message.content
                  )}
                </div>
                <div className={styles.chatMessageMeta}>
                  {message.date}
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input */}
      <div className={styles.chatInputPanel}>
        <div className={styles.chatInputContainer}>
          <textarea
            ref={inputRef}
            className={styles.chatInput}
            placeholder="Type your message here..."
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
            rows={1}
          />
          <IconButton
            icon={isLoading ? <LoadingIcon /> : <SendIcon />}
            onClick={handleSubmit}
            disabled={!userInput.trim() || isLoading}
            className={styles.chatSendButton}
          />
        </div>
      </div>
    </div>
  )
})
