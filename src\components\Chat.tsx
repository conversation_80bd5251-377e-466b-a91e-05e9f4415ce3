import { observer } from 'mobx-react-lite'
import { useState, useRef, useEffect, useCallback } from 'react'
import { useStores } from '../stores'
import { useKeyboardShortcuts, KEYBOARD_SHORTCUTS } from '../hooks'
import { IconButton } from './ui/IconButton'
import { MessageActions } from './MessageActions'
import { MessageEditor } from './MessageEditor'
import { ShortcutHelp } from './ShortcutHelp'
import type { ChatMessage } from '../stores/ChatStore'
import styles from './Chat.module.scss'
import clsx from 'clsx'

// 更好看的图标
const SendIcon = () => <span>📤</span>
const LoadingIcon = () => <span>⏳</span>
const NewChatIcon = () => <span>➕</span>
const ClearIcon = () => <span>🗑️</span>
const HelpIcon = () => <span>❓</span>

export const Chat = observer(() => {
  const { chatStore } = useStores()
  const [userInput, setUserInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [editingMessage, setEditingMessage] = useState<ChatMessage | null>(null)
  const [showShortcutHelp, setShowShortcutHelp] = useState(false)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const currentSession = chatStore.currentSession
  const messages = currentSession.messages

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  // 自动调整输入框高度
  const adjustTextareaHeight = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.style.height = 'auto'
      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 200)}px`
    }
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  useEffect(() => {
    adjustTextareaHeight()
  }, [userInput, adjustTextareaHeight])

  const handleSubmit = async () => {
    if (!userInput.trim() || isLoading) return

    const input = userInput.trim()
    setUserInput('')
    setIsLoading(true)

    try {
      await chatStore.onUserInput(input)
    } catch (error) {
      console.error('Failed to send message:', error)
      // 如果发送失败，恢复输入内容
      setUserInput(input)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  // 消息操作处理函数
  const handleEditMessage = (message: ChatMessage) => {
    setEditingMessage(message)
  }

  const handleDeleteMessage = (messageId: string) => {
    chatStore.deleteMessage(messageId)
  }

  const handleResendMessage = async (message: ChatMessage) => {
    setIsLoading(true)
    try {
      await chatStore.resendMessage(message)
    } catch (error) {
      console.error('Failed to resend message:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handlePinMessage = (message: ChatMessage) => {
    chatStore.pinMessage(message)
  }

  const handleSaveEdit = (messageId: string, newContent: string) => {
    chatStore.editMessage(messageId, newContent)
    setEditingMessage(null)
  }

  // 快捷键处理
  const handleNewChat = () => {
    chatStore.newSession()
  }

  const handleClearChat = () => {
    const session = chatStore.currentSession
    chatStore.updateCurrentSession(session => {
      session.messages = []
    })
  }

  const handleCopyLastMessage = () => {
    const messages = chatStore.currentSession.messages
    const lastMessage = messages[messages.length - 1]
    if (lastMessage) {
      navigator.clipboard.writeText(lastMessage.content)
    }
  }

  const handleFocusInput = () => {
    inputRef.current?.focus()
  }

  const handleShowShortcutHelp = () => {
    setShowShortcutHelp(true)
  }

  // 设置快捷键
  useKeyboardShortcuts([
    {
      ...KEYBOARD_SHORTCUTS.NEW_CHAT,
      callback: handleNewChat,
    },
    {
      ...KEYBOARD_SHORTCUTS.CLEAR_CHAT,
      callback: handleClearChat,
    },
    {
      ...KEYBOARD_SHORTCUTS.COPY_LAST,
      callback: handleCopyLastMessage,
    },
    {
      ...KEYBOARD_SHORTCUTS.FOCUS_INPUT,
      callback: handleFocusInput,
    },
    {
      key: '?',
      callback: handleShowShortcutHelp,
      description: '?: 显示快捷键帮助',
    },
  ])

  return (
    <div className={styles.chat}>
      {/* Header */}
      <div className={styles.chatHeader}>
        <div className={styles.chatHeaderTitle}>
          <div className={styles.chatHeaderMainTitle}>{currentSession.topic || 'NextChat'}</div>
          <div className={styles.chatHeaderSubTitle}>
            {messages.length} messages • {isLoading ? 'AI is typing...' : 'Ready'}
          </div>
        </div>
        <div className={styles.chatHeaderActions}>
          <IconButton
            icon={<NewChatIcon />}
            onClick={handleNewChat}
            title="新建聊天 (Ctrl+N)"
            className={styles.headerButton}
          />
          <IconButton
            icon={<ClearIcon />}
            onClick={handleClearChat}
            title="清空聊天 (Ctrl+L)"
            className={styles.headerButton}
          />
          <IconButton
            icon={<HelpIcon />}
            onClick={handleShowShortcutHelp}
            title="快捷键帮助 (?)"
            className={styles.headerButton}
          />
        </div>
      </div>

      {/* Messages */}
      <div className={styles.chatBody}>
        <div className={styles.chatMessageContainer}>
          {messages.length === 0 ? (
            <div className={styles.chatWelcome}>
              <div className={styles.chatWelcomeTitle}>Welcome to NextChat</div>
              <div className={styles.chatWelcomeSubTitle}>Start a conversation with AI</div>
            </div>
          ) : (
            messages.map(message => (
              <div
                key={message.id}
                className={clsx(styles.chatMessage, styles.messageContainer, {
                  [styles.chatMessageUser]: message.role === 'user',
                  [styles.chatMessageAssistant]: message.role === 'assistant',
                })}
              >
                <div className={styles.chatMessageContent}>
                  {message.streaming ? (
                    <div className={styles.chatMessageStreaming}>
                      <LoadingIcon />
                      {message.content}
                    </div>
                  ) : (
                    <div className={styles.messageText}>{message.content}</div>
                  )}
                </div>
                <div className={styles.chatMessageMeta}>{message.date}</div>

                {/* 消息操作按钮 */}
                {!message.streaming && (
                  <MessageActions
                    message={message}
                    onEdit={handleEditMessage}
                    onDelete={handleDeleteMessage}
                    onResend={handleResendMessage}
                    onPin={handlePinMessage}
                    className={styles.messageActions}
                  />
                )}
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input */}
      <div className={styles.chatInputPanel}>
        <div className={styles.chatInputContainer}>
          <textarea
            ref={inputRef}
            className={styles.chatInput}
            placeholder="Type your message here... (Enter to send, Shift+Enter for new line)"
            value={userInput}
            onChange={e => setUserInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
            rows={1}
            style={{ minHeight: '20px', maxHeight: '200px' }}
          />
          <IconButton
            icon={isLoading ? <LoadingIcon /> : <SendIcon />}
            onClick={handleSubmit}
            disabled={!userInput.trim() || isLoading}
            className={styles.chatSendButton}
          />
        </div>
      </div>

      {/* 消息编辑器 */}
      {editingMessage && (
        <MessageEditor
          message={editingMessage}
          isOpen={!!editingMessage}
          onClose={() => setEditingMessage(null)}
          onSave={handleSaveEdit}
        />
      )}

      {/* 快捷键帮助 */}
      <ShortcutHelp isOpen={showShortcutHelp} onClose={() => setShowShortcutHelp(false)} />
    </div>
  )
})
