import { observer } from 'mobx-react-lite'
import { useState, useRef, useEffect, useCallback } from 'react'
import { useStores } from '../stores'
import { IconButton } from './ui/IconButton'
import styles from './Chat.module.scss'
import clsx from 'clsx'

// 更好看的图标
const SendIcon = () => <span>📤</span>
const LoadingIcon = () => <span>⏳</span>

export const Chat = observer(() => {
  const { chatStore } = useStores()
  const [userInput, setUserInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const currentSession = chatStore.currentSession
  const messages = currentSession.messages

  const scrollToBottom = useCallback(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [])

  // 自动调整输入框高度
  const adjustTextareaHeight = useCallback(() => {
    if (inputRef.current) {
      inputRef.current.style.height = 'auto'
      inputRef.current.style.height = `${Math.min(inputRef.current.scrollHeight, 200)}px`
    }
  }, [])

  useEffect(() => {
    scrollToBottom()
  }, [messages, scrollToBottom])

  useEffect(() => {
    adjustTextareaHeight()
  }, [userInput, adjustTextareaHeight])

  const handleSubmit = async () => {
    if (!userInput.trim() || isLoading) return

    const input = userInput.trim()
    setUserInput('')
    setIsLoading(true)

    try {
      await chatStore.onUserInput(input)
    } catch (error) {
      console.error('Failed to send message:', error)
      // 如果发送失败，恢复输入内容
      setUserInput(input)
    } finally {
      setIsLoading(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  return (
    <div className={styles.chat}>
      {/* Header */}
      <div className={styles.chatHeader}>
        <div className={styles.chatHeaderTitle}>
          <div className={styles.chatHeaderMainTitle}>{currentSession.topic || 'NextChat'}</div>
          <div className={styles.chatHeaderSubTitle}>
            {messages.length} messages • {isLoading ? 'AI is typing...' : 'Ready'}
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className={styles.chatBody}>
        <div className={styles.chatMessageContainer}>
          {messages.length === 0 ? (
            <div className={styles.chatWelcome}>
              <div className={styles.chatWelcomeTitle}>Welcome to NextChat</div>
              <div className={styles.chatWelcomeSubTitle}>Start a conversation with AI</div>
            </div>
          ) : (
            messages.map(message => (
              <div
                key={message.id}
                className={clsx(styles.chatMessage, {
                  [styles.chatMessageUser]: message.role === 'user',
                  [styles.chatMessageAssistant]: message.role === 'assistant',
                })}
              >
                <div className={styles.chatMessageContent}>
                  {message.streaming ? (
                    <div className={styles.chatMessageStreaming}>
                      <LoadingIcon />
                      {message.content}
                    </div>
                  ) : (
                    message.content
                  )}
                </div>
                <div className={styles.chatMessageMeta}>{message.date}</div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input */}
      <div className={styles.chatInputPanel}>
        <div className={styles.chatInputContainer}>
          <textarea
            ref={inputRef}
            className={styles.chatInput}
            placeholder="Type your message here... (Enter to send, Shift+Enter for new line)"
            value={userInput}
            onChange={e => setUserInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={isLoading}
            rows={1}
            style={{ minHeight: '20px', maxHeight: '200px' }}
          />
          <IconButton
            icon={isLoading ? <LoadingIcon /> : <SendIcon />}
            onClick={handleSubmit}
            disabled={!userInput.trim() || isLoading}
            className={styles.chatSendButton}
          />
        </div>
      </div>
    </div>
  )
})
