.chatList {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chatListHeader {
  padding: 10px 15px;
  font-size: 12px;
  font-weight: bold;
  color: var(--black);
  opacity: 0.7;
  border-bottom: var(--border-in-light);
}

.chatListBody {
  flex: 1;
  overflow-y: auto;
}

.chatItem {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: var(--border-in-light);
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--hover-color);
  }

  &.chatItemActive {
    background-color: var(--second);
  }

  &.chatItemNarrow {
    justify-content: center;
    padding: 10px 5px;
  }
}

.chatItemContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.chatItemTitle {
  font-size: 14px;
  font-weight: 500;
  color: var(--black);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chatItemPreview {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chatItemActions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.2s ease;

  .chatItem:hover & {
    opacity: 1;
  }
}

.chatItemAction {
  min-width: 24px;
  min-height: 24px;
  padding: 4px;
}
