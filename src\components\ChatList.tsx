import { observer } from 'mobx-react-lite'
import { useStores } from '../stores'
import { IconButton } from './ui/IconButton'
import styles from './ChatList.module.scss'
import clsx from 'clsx'

interface ChatListProps {
  narrow?: boolean
}

const DeleteIcon = () => <span>×</span>
const EditIcon = () => <span>✎</span>

export const ChatList = observer(({ narrow }: ChatListProps) => {
  const { chatStore } = useStores()

  const handleSelectSession = (index: number) => {
    chatStore.selectSession(index)
  }

  const handleDeleteSession = (index: number, e: React.MouseEvent) => {
    e.stopPropagation()
    if (window.confirm('Delete this chat?')) {
      chatStore.deleteSession(index)
    }
  }

  return (
    <div className={styles.chatList}>
      <div className={styles.chatListHeader}>
        <span>Chats</span>
      </div>
      <div className={styles.chatListBody}>
        {chatStore.sessions.map((session, index) => (
          <div
            key={session.id}
            className={clsx(styles.chatItem, {
              [styles.chatItemActive]: index === chatStore.currentSessionIndex,
              [styles.chatItemNarrow]: narrow,
            })}
            onClick={() => handleSelectSession(index)}
          >
            <div className={styles.chatItemContent}>
              <div className={styles.chatItemTitle}>
                {session.topic}
              </div>
              {!narrow && (
                <div className={styles.chatItemPreview}>
                  {session.messages.length > 0
                    ? session.messages[session.messages.length - 1].content.slice(0, 50) + '...'
                    : 'No messages'
                  }
                </div>
              )}
            </div>
            <div className={styles.chatItemActions}>
              <IconButton
                icon={<DeleteIcon />}
                onClick={(e) => handleDeleteSession(index, e as any)}
                className={styles.chatItemAction}
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
})
