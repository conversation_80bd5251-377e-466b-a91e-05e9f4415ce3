@mixin container {
  background-color: var(--white);
  border: var(--border-in-light);
  border-radius: 20px;
  box-shadow: var(--shadow);
  color: var(--black);
  background-color: var(--white);
  min-width: 600px;
  min-height: 370px;
  max-width: 1200px;

  display: flex;
  overflow: hidden;
  box-sizing: border-box;

  width: var(--window-width);
  height: var(--window-height);
}

.container {
  @include container();
}

@media only screen and (min-width: 600px) {
  .tightContainer {
    --window-width: 100vw;
    --window-height: var(--full-height);
    --window-content-width: calc(100% - var(--sidebar-width));

    @include container();

    max-width: 100vw;
    max-height: var(--full-height);

    border-radius: 0;
    border: 0;
  }
}

.windowContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebarShow {
  display: flex !important;
}

@media only screen and (max-width: 600px) {
  .container {
    min-height: unset;
    min-width: unset;
    max-height: unset;
    min-width: unset;
    border: 0;
    border-radius: 0;
  }

  .sidebarShow {
    display: none !important;
  }
}
