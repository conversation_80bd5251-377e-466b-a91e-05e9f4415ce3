@mixin container {
  background-color: var(--white);
  color: var(--black);
  display: flex;
  overflow: hidden;
  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  border: none;
  border-radius: 0;
}

.container {
  @include container();
}

.tightContainer {
  @include container();
}

.windowContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100vh;
}

.sidebarShow {
  display: flex !important;
}

@media only screen and (max-width: 600px) {
  .sidebarShow {
    display: none !important;
  }
}
