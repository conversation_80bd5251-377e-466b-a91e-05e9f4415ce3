import { Outlet, useLocation } from 'react-router-dom'
import { observer } from 'mobx-react-lite'
import { Sidebar } from './Sidebar'
import { useStores } from '../stores'
import styles from './Home.module.scss'
import clsx from 'clsx'

export const Home = observer(() => {
  const location = useLocation()
  const { configStore } = useStores()
  
  const isHome = location.pathname === '/'
  const isSettings = location.pathname === '/settings'
  
  return (
    <div className={clsx(styles.container, {
      [styles.tightContainer]: configStore.config.tightBorder
    })}>
      {!isSettings && (
        <Sidebar 
          className={clsx({
            [styles.sidebarShow]: isHome,
          })}
        />
      )}
      <div className={styles.windowContent}>
        <Outlet />
      </div>
    </div>
  )
})
