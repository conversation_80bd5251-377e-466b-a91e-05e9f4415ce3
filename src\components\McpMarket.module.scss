.mcpMarket {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.mcpHeader {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 32px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 10px;
  }

  p {
    font-size: 16px;
    color: var(--black);
    opacity: 0.7;
  }
}

.mcpContent {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.emptyState {
  text-align: center;
  padding: 40px;
  color: var(--black);
  opacity: 0.5;
}
