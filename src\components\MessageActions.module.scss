.messageActions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  padding: 4px;
  background-color: var(--white);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: absolute;
  top: -40px;
  right: 10px;
  z-index: 10;

  &:hover {
    opacity: 1;
  }
}

.actionButton {
  min-width: 28px;
  min-height: 28px;
  padding: 4px;
  font-size: 12px;
  border-radius: 4px;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--hover-color);
    transform: scale(1.1);
  }
}

.deleteButton {
  &:hover {
    background-color: #ff4d4f;
    color: white;
  }
}

.confirmContent {
  padding: 20px;
  text-align: center;

  p {
    margin-bottom: 20px;
    color: var(--black);
    line-height: 1.5;
  }
}

.confirmActions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.cancelButton,
.confirmButton {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.cancelButton {
  background-color: var(--gray);
  color: var(--black);

  &:hover {
    background-color: var(--hover-color);
  }
}

.confirmButton {
  background-color: #ff4d4f;
  color: white;

  &:hover {
    background-color: #ff7875;
  }
}

// 当消息被悬停时显示操作按钮
.messageContainer:hover .messageActions {
  opacity: 1;
}

// 响应式设计
@media (max-width: 768px) {
  .messageActions {
    position: static;
    opacity: 1;
    margin-top: 8px;
    justify-content: center;
    box-shadow: none;
    background-color: transparent;
  }

  .actionButton {
    min-width: 32px;
    min-height: 32px;
    padding: 6px;
  }
}
