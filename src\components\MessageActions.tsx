import React, { useState } from 'react'
import { observer } from 'mobx-react-lite'
import { IconButton } from './ui/IconButton'
import { Modal } from './ui/Modal'
import { useStores } from '../stores'
import { useCopyToClipboard } from '../hooks'
import type { ChatMessage } from '../stores/ChatStore'
import styles from './MessageActions.module.scss'
import clsx from 'clsx'

interface MessageActionsProps {
  message: ChatMessage
  onEdit?: (message: ChatMessage) => void
  onDelete?: (messageId: string) => void
  onResend?: (message: ChatMessage) => void
  onPin?: (message: ChatMessage) => void
  className?: string
}

// 图标组件
const CopyIcon = () => <span>📋</span>
const EditIcon = () => <span>✏️</span>
const DeleteIcon = () => <span>🗑️</span>
const ResendIcon = () => <span>🔄</span>
const PinIcon = () => <span>📌</span>
const SpeakIcon = () => <span>🔊</span>
const StopSpeakIcon = () => <span>🔇</span>

export const MessageActions: React.FC<MessageActionsProps> = observer(({
  message,
  onEdit,
  onDelete,
  onResend,
  onPin,
  className,
}) => {
  const { copyToClipboard, isCopied } = useCopyToClipboard()
  const [showConfirm, setShowConfirm] = useState(false)
  const [isSpeaking, setIsSpeaking] = useState(false)

  const handleCopy = () => {
    copyToClipboard(message.content)
  }

  const handleEdit = () => {
    onEdit?.(message)
  }

  const handleDelete = () => {
    setShowConfirm(true)
  }

  const confirmDelete = () => {
    onDelete?.(message.id)
    setShowConfirm(false)
  }

  const handleResend = () => {
    onResend?.(message)
  }

  const handlePin = () => {
    onPin?.(message)
  }

  const handleSpeak = () => {
    if (isSpeaking) {
      // 停止语音
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel()
      }
      setIsSpeaking(false)
    } else {
      // 开始语音
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(message.content)
        utterance.onend = () => setIsSpeaking(false)
        utterance.onerror = () => setIsSpeaking(false)
        window.speechSynthesis.speak(utterance)
        setIsSpeaking(true)
      }
    }
  }

  return (
    <>
      <div className={clsx(styles.messageActions, className)}>
        <IconButton
          icon={isCopied ? <span>✅</span> : <CopyIcon />}
          onClick={handleCopy}
          title="复制消息"
          className={styles.actionButton}
        />
        
        {message.role === 'user' && (
          <IconButton
            icon={<EditIcon />}
            onClick={handleEdit}
            title="编辑消息"
            className={styles.actionButton}
          />
        )}
        
        <IconButton
          icon={<ResendIcon />}
          onClick={handleResend}
          title="重新生成"
          className={styles.actionButton}
        />
        
        <IconButton
          icon={<PinIcon />}
          onClick={handlePin}
          title="固定消息"
          className={styles.actionButton}
        />
        
        {message.role === 'assistant' && (
          <IconButton
            icon={isSpeaking ? <StopSpeakIcon /> : <SpeakIcon />}
            onClick={handleSpeak}
            title={isSpeaking ? '停止朗读' : '朗读消息'}
            className={styles.actionButton}
          />
        )}
        
        <IconButton
          icon={<DeleteIcon />}
          onClick={handleDelete}
          title="删除消息"
          className={clsx(styles.actionButton, styles.deleteButton)}
        />
      </div>

      {showConfirm && (
        <Modal
          title="确认删除"
          isOpen={showConfirm}
          onClose={() => setShowConfirm(false)}
          size="small"
        >
          <div className={styles.confirmContent}>
            <p>确定要删除这条消息吗？此操作无法撤销。</p>
            <div className={styles.confirmActions}>
              <button
                className={styles.cancelButton}
                onClick={() => setShowConfirm(false)}
              >
                取消
              </button>
              <button
                className={styles.confirmButton}
                onClick={confirmDelete}
              >
                删除
              </button>
            </div>
          </div>
        </Modal>
      )}
    </>
  )
})
