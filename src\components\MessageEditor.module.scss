.editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 400px;
}

.editorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 15px 0;
  border-bottom: var(--border-in-light);
  margin-bottom: 15px;
}

.roleLabel {
  font-weight: bold;
  color: var(--primary);
  font-size: 14px;
}

.dateLabel {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
}

.editorBody {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.textarea {
  flex: 1;
  width: 100%;
  min-height: 200px;
  padding: 15px;
  border: var(--border-in-light);
  border-radius: 8px;
  font-size: 14px;
  line-height: 1.6;
  font-family: inherit;
  resize: vertical;
  outline: none;
  background-color: var(--white);
  color: var(--black);
  transition: border-color 0.2s ease, box-shadow 0.2s ease;

  &:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 2px rgba(29, 147, 171, 0.2);
  }

  &::placeholder {
    color: var(--black);
    opacity: 0.5;
  }
}

.hint {
  font-size: 12px;
  color: var(--black);
  opacity: 0.6;
  text-align: center;
  padding: 5px;
  background-color: var(--gray);
  border-radius: 4px;
}

.editorFooter {
  padding-top: 15px;
  border-top: var(--border-in-light);
  margin-top: 15px;
}

.actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.cancelButton,
.saveButton {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
}

.cancelButton {
  background-color: var(--gray);
  color: var(--black);

  &:hover {
    background-color: var(--hover-color);
  }
}

.saveButton {
  background-color: var(--primary);
  color: white;

  &:hover:not(:disabled) {
    background-color: #1a8fa3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(29, 147, 171, 0.3);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .editor {
    min-height: 300px;
  }

  .textarea {
    min-height: 150px;
  }

  .actions {
    flex-direction: column-reverse;
  }

  .cancelButton,
  .saveButton {
    width: 100%;
    justify-content: center;
  }
}
