import React, { useState, useRef, useEffect } from 'react'
import { Modal } from './ui/Modal'
import { IconButton } from './ui/IconButton'
import type { ChatMessage } from '../stores/ChatStore'
import styles from './MessageEditor.module.scss'

interface MessageEditorProps {
  message: ChatMessage
  isOpen: boolean
  onClose: () => void
  onSave: (messageId: string, newContent: string) => void
}

const SaveIcon = () => <span>💾</span>
const CancelIcon = () => <span>❌</span>

export const MessageEditor: React.FC<MessageEditorProps> = ({
  message,
  isOpen,
  onClose,
  onSave,
}) => {
  const [content, setContent] = useState(message.content)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (isOpen) {
      setContent(message.content)
      // 延迟聚焦，确保模态框已经渲染
      setTimeout(() => {
        textareaRef.current?.focus()
        textareaRef.current?.select()
      }, 100)
    }
  }, [isOpen, message.content])

  const handleSave = () => {
    if (content.trim() !== message.content) {
      onSave(message.id, content.trim())
    }
    onClose()
  }

  const handleCancel = () => {
    setContent(message.content) // 恢复原内容
    onClose()
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      handleSave()
    } else if (e.key === 'Escape') {
      e.preventDefault()
      handleCancel()
    }
  }

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }

  useEffect(() => {
    adjustTextareaHeight()
  }, [content])

  return (
    <Modal
      title="编辑消息"
      isOpen={isOpen}
      onClose={handleCancel}
      size="medium"
    >
      <div className={styles.editor}>
        <div className={styles.editorHeader}>
          <span className={styles.roleLabel}>
            {message.role === 'user' ? '👤 用户' : '🤖 助手'}
          </span>
          <span className={styles.dateLabel}>
            {message.date}
          </span>
        </div>
        
        <div className={styles.editorBody}>
          <textarea
            ref={textareaRef}
            className={styles.textarea}
            value={content}
            onChange={(e) => setContent(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="输入消息内容..."
            rows={5}
          />
          
          <div className={styles.hint}>
            <span>💡 提示：Ctrl+Enter 保存，Esc 取消</span>
          </div>
        </div>
        
        <div className={styles.editorFooter}>
          <div className={styles.actions}>
            <IconButton
              icon={<CancelIcon />}
              onClick={handleCancel}
              className={styles.cancelButton}
            >
              取消
            </IconButton>
            <IconButton
              icon={<SaveIcon />}
              onClick={handleSave}
              className={styles.saveButton}
              disabled={!content.trim() || content.trim() === message.content}
            >
              保存
            </IconButton>
          </div>
        </div>
      </div>
    </Modal>
  )
}
