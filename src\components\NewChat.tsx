import { observer } from 'mobx-react-lite'
import { useNavigate } from 'react-router-dom'
import { useStores } from '../stores'
import { IconButton } from './ui/IconButton'
import styles from './NewChat.module.scss'

export const NewChat = observer(() => {
  const navigate = useNavigate()
  const { chatStore } = useStores()

  const handleStartChat = () => {
    chatStore.newSession()
    navigate('/chat')
  }

  return (
    <div className={styles.newChat}>
      <div className={styles.newChatHeader}>
        <h1>Start New Chat</h1>
        <p>Choose how you want to start your conversation</p>
      </div>
      
      <div className={styles.newChatContent}>
        <div className={styles.newChatOption}>
          <h3>Blank Chat</h3>
          <p>Start with a clean slate</p>
          <IconButton
            text="Start Chat"
            onClick={handleStartChat}
            bordered
          />
        </div>
      </div>
    </div>
  )
})
