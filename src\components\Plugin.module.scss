.pluginPage {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.pluginHeader {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 32px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 10px;
  }

  p {
    font-size: 16px;
    color: var(--black);
    opacity: 0.7;
  }
}

.pluginContent {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.pluginSection {
  h3 {
    font-size: 20px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 20px;
  }
}

.emptyState {
  text-align: center;
  padding: 40px;
  color: var(--black);
  opacity: 0.5;
}

.pluginList {
  display: grid;
  gap: 15px;
}

.pluginItem {
  padding: 20px;
  border: var(--border-in-light);
  border-radius: 10px;
  background-color: var(--white);
}

.pluginInfo {
  h4 {
    font-size: 16px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 5px;
  }

  p {
    font-size: 12px;
    color: var(--black);
    opacity: 0.7;
  }
}
