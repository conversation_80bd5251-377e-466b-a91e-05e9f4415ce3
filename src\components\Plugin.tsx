import { observer } from 'mobx-react-lite'
import { useStores } from '../stores'
import styles from './Plugin.module.scss'

export const PluginPage = observer(() => {
  const { pluginStore } = useStores()

  return (
    <div className={styles.pluginPage}>
      <div className={styles.pluginHeader}>
        <h1>Plugins</h1>
        <p>Manage your plugins and extensions</p>
      </div>
      
      <div className={styles.pluginContent}>
        <div className={styles.pluginSection}>
          <h3>Installed Plugins</h3>
          {pluginStore.allPlugins.length === 0 ? (
            <div className={styles.emptyState}>
              <p>No plugins installed yet</p>
            </div>
          ) : (
            <div className={styles.pluginList}>
              {pluginStore.allPlugins.map(plugin => (
                <div key={plugin.id} className={styles.pluginItem}>
                  <div className={styles.pluginInfo}>
                    <h4>{plugin.title}</h4>
                    <p>Version {plugin.version}</p>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
})
