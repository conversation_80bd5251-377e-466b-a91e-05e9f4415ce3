.searchChat {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.searchHeader {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    font-size: 32px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 10px;
  }

  p {
    font-size: 16px;
    color: var(--black);
    opacity: 0.7;
  }
}

.searchContent {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.searchInput {
  input {
    width: 100%;
    padding: 15px;
    border: var(--border-in-light);
    border-radius: 10px;
    font-size: 16px;
    background-color: var(--white);
    color: var(--black);

    &:focus {
      outline: none;
      border-color: var(--primary);
    }
  }
}

.searchResults {
  h3 {
    font-size: 18px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 20px;
  }
}

.emptyState {
  text-align: center;
  padding: 40px;
  color: var(--black);
  opacity: 0.5;
}

.resultList {
  display: grid;
  gap: 15px;
}

.resultItem {
  padding: 20px;
  border: var(--border-in-light);
  border-radius: 10px;
  background-color: var(--white);
  cursor: pointer;

  &:hover {
    background-color: var(--hover-color);
  }

  h4 {
    font-size: 16px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 5px;
  }

  p {
    font-size: 12px;
    color: var(--black);
    opacity: 0.7;
  }
}
