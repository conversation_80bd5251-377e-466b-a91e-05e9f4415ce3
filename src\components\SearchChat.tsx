import { observer } from 'mobx-react-lite'
import { useState } from 'react'
import { useStores } from '../stores'
import styles from './SearchChat.module.scss'

export const SearchChat = observer(() => {
  const [searchQuery, setSearchQuery] = useState('')
  const { chatStore } = useStores()

  const searchResults = chatStore.sessions.filter(session =>
    session.topic.toLowerCase().includes(searchQuery.toLowerCase()) ||
    session.messages.some(msg => 
      msg.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
  )

  return (
    <div className={styles.searchChat}>
      <div className={styles.searchHeader}>
        <h1>Search Chat History</h1>
        <p>Find messages and conversations</p>
      </div>
      
      <div className={styles.searchContent}>
        <div className={styles.searchInput}>
          <input
            type="text"
            placeholder="Search messages..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        <div className={styles.searchResults}>
          {searchQuery && (
            <>
              <h3>Results ({searchResults.length})</h3>
              {searchResults.length === 0 ? (
                <div className={styles.emptyState}>
                  <p>No results found</p>
                </div>
              ) : (
                <div className={styles.resultList}>
                  {searchResults.map(session => (
                    <div key={session.id} className={styles.resultItem}>
                      <h4>{session.topic}</h4>
                      <p>{session.messages.length} messages</p>
                    </div>
                  ))}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
})
