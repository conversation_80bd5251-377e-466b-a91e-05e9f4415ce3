.settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--white);
}

.settingsHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: var(--border-in-light);
}

.settingsTitle {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.settingsMainTitle {
  font-size: 20px;
  font-weight: bold;
  color: var(--black);
}

.settingsSubTitle {
  font-size: 12px;
  color: var(--black);
  opacity: 0.5;
}

.settingsActions {
  display: flex;
  gap: 10px;
}

.settingsContent {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.settingsSection {
  margin-bottom: 30px;

  h3 {
    font-size: 16px;
    font-weight: bold;
    color: var(--black);
    margin-bottom: 15px;
    padding-bottom: 5px;
    border-bottom: var(--border-in-light);
  }
}

.settingsItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 0;
  gap: 20px;

  label {
    font-size: 14px;
    color: var(--black);
    min-width: 100px;
  }

  select, input {
    flex: 1;
    max-width: 200px;
  }

  span {
    font-size: 12px;
    color: var(--black);
    opacity: 0.7;
    min-width: 40px;
    text-align: right;
  }
}
