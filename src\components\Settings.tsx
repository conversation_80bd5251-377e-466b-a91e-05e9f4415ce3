import { observer } from 'mobx-react-lite'
import { useNavigate } from 'react-router-dom'
import { useStores } from '../stores'
import { IconButton } from './ui/IconButton'
import styles from './Settings.module.scss'

const CloseIcon = () => <span>×</span>

export const Settings = observer(() => {
  const navigate = useNavigate()
  const { configStore } = useStores()

  return (
    <div className={styles.settings}>
      <div className={styles.settingsHeader}>
        <div className={styles.settingsTitle}>
          <div className={styles.settingsMainTitle}>Settings</div>
          <div className={styles.settingsSubTitle}>Configure your preferences</div>
        </div>
        <div className={styles.settingsActions}>
          <IconButton
            icon={<CloseIcon />}
            onClick={() => navigate('/')}
            bordered
          />
        </div>
      </div>

      <div className={styles.settingsContent}>
        <div className={styles.settingsSection}>
          <h3>Appearance</h3>
          <div className={styles.settingsItem}>
            <label>Theme</label>
            <select
              value={configStore.theme}
              onChange={(e) => configStore.update(config => {
                config.theme = e.target.value as any
              })}
            >
              <option value="auto">Auto</option>
              <option value="light">Light</option>
              <option value="dark">Dark</option>
            </select>
          </div>
          <div className={styles.settingsItem}>
            <label>Font Size</label>
            <input
              type="range"
              min="12"
              max="18"
              value={configStore.fontSize}
              onChange={(e) => configStore.update(config => {
                config.fontSize = parseInt(e.target.value)
              })}
            />
            <span>{configStore.fontSize}px</span>
          </div>
        </div>

        <div className={styles.settingsSection}>
          <h3>Model</h3>
          <div className={styles.settingsItem}>
            <label>Model</label>
            <select
              value={configStore.model}
              onChange={(e) => configStore.updateModelConfig(config => {
                config.model = e.target.value
              })}
            >
              <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
              <option value="gpt-4">GPT-4</option>
              <option value="gpt-4-turbo">GPT-4 Turbo</option>
            </select>
          </div>
          <div className={styles.settingsItem}>
            <label>Temperature</label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={configStore.config.modelConfig.temperature}
              onChange={(e) => configStore.updateModelConfig(config => {
                config.temperature = parseFloat(e.target.value)
              })}
            />
            <span>{configStore.config.modelConfig.temperature}</span>
          </div>
        </div>
      </div>
    </div>
  )
})
