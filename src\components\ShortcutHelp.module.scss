.shortcutHelp {
  padding: 10px 0;
}

.category {
  margin-bottom: 30px;

  &:last-child {
    margin-bottom: 20px;
  }
}

.categoryTitle {
  font-size: 16px;
  font-weight: bold;
  color: var(--primary);
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--primary);
}

.shortcutList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shortcutItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background-color: var(--gray);
  border-radius: 6px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--hover-color);
  }
}

.keys {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 120px;
}

.key {
  display: inline-block;
  padding: 4px 8px;
  background-color: var(--white);
  border: 1px solid var(--border-in-light);
  border-radius: 4px;
  font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
  font-size: 12px;
  font-weight: bold;
  color: var(--black);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  min-width: 24px;
  text-align: center;
}

.plus {
  color: var(--black);
  opacity: 0.6;
  font-size: 12px;
  margin: 0 2px;
}

.description {
  flex: 1;
  color: var(--black);
  font-size: 14px;
  text-align: right;
}

.footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: var(--border-in-light);
}

.tip {
  color: var(--black);
  opacity: 0.7;
  font-size: 13px;
  text-align: center;
  margin: 0;
  padding: 10px;
  background-color: var(--gray);
  border-radius: 6px;
  line-height: 1.5;
}

// 响应式设计
@media (max-width: 768px) {
  .shortcutItem {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .keys {
    min-width: auto;
  }

  .description {
    text-align: left;
    font-size: 13px;
  }

  .key {
    font-size: 11px;
    padding: 3px 6px;
  }
}
