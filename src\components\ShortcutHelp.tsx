import React from 'react'
import { Modal } from './ui/Modal'
import { KEYBOARD_SHORTCUTS } from '../hooks'
import styles from './ShortcutHelp.module.scss'

interface ShortcutHelpProps {
  isOpen: boolean
  onClose: () => void
}

export const ShortcutHelp: React.FC<ShortcutHelpProps> = ({ isOpen, onClose }) => {
  const shortcuts = [
    {
      category: '聊天操作',
      items: [
        { keys: 'Ctrl + N', description: '新建聊天' },
        { keys: 'Ctrl + L', description: '清空当前聊天' },
        { keys: 'Enter', description: '发送消息' },
        { keys: 'Shift + Enter', description: '换行' },
        { keys: '/', description: '聚焦输入框' },
      ],
    },
    {
      category: '消息操作',
      items: [
        { keys: 'Ctrl + Shift + C', description: '复制最后一条消息' },
        { keys: 'Hover + 📋', description: '复制消息' },
        { keys: 'Hover + ✏️', description: '编辑消息' },
        { keys: 'Hover + 🔄', description: '重新生成' },
        { keys: 'Hover + 📌', description: '固定消息' },
      ],
    },
    {
      category: '编辑器',
      items: [
        { keys: 'Ctrl + Enter', description: '保存编辑' },
        { keys: 'Esc', description: '取消编辑' },
      ],
    },
    {
      category: '通用',
      items: [
        { keys: 'Esc', description: '关闭弹窗' },
        { keys: '?', description: '显示快捷键帮助' },
      ],
    },
  ]

  return (
    <Modal
      title="⌨️ 快捷键帮助"
      isOpen={isOpen}
      onClose={onClose}
      size="medium"
    >
      <div className={styles.shortcutHelp}>
        {shortcuts.map((category, index) => (
          <div key={index} className={styles.category}>
            <h3 className={styles.categoryTitle}>{category.category}</h3>
            <div className={styles.shortcutList}>
              {category.items.map((shortcut, shortcutIndex) => (
                <div key={shortcutIndex} className={styles.shortcutItem}>
                  <div className={styles.keys}>
                    {shortcut.keys.split(' + ').map((key, keyIndex) => (
                      <React.Fragment key={keyIndex}>
                        {keyIndex > 0 && <span className={styles.plus}>+</span>}
                        <kbd className={styles.key}>{key}</kbd>
                      </React.Fragment>
                    ))}
                  </div>
                  <div className={styles.description}>{shortcut.description}</div>
                </div>
              ))}
            </div>
          </div>
        ))}
        
        <div className={styles.footer}>
          <p className={styles.tip}>
            💡 提示：将鼠标悬停在消息上可以看到更多操作选项
          </p>
        </div>
      </div>
    </Modal>
  )
}
