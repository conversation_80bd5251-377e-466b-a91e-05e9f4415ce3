.sidebar {
  top: 0;
  width: var(--sidebar-width);
  box-sizing: border-box;
  padding: 20px;
  background-color: var(--second);
  display: flex;
  flex-direction: column;
  box-shadow: inset -2px 0px 2px 0px rgb(0, 0, 0, 0.05);
  position: relative;
  transition: width ease 0.05s;
  height: 100vh;
  overflow: hidden;

  &.narrowSidebar {
    width: var(--sidebar-narrow-width, 100px);
  }
}

.sidebarHeader {
  position: relative;
  padding-top: 20px;
  padding-bottom: 20px;
}

.sidebarHeaderNarrow {
  .sidebarTitleContainer {
    align-items: center;
    width: 100%;
    max-height: 0;
    overflow: hidden;
  }

  .sidebarTitle,
  .sidebarSubTitle {
    opacity: 0;
    text-align: center;
  }
}

.sidebarTitleContainer {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.sidebarTitle {
  font-size: 20px;
  font-weight: bold;
  animation: slide-in ease 0.3s;
}

.sidebarSubTitle {
  font-size: 12px;
  font-weight: 400;
  animation: slide-in ease 0.3s;
  opacity: 0.7;
}

.sidebarLogo {
  position: absolute;
  right: 0;
  bottom: 18px;

  svg {
    width: 30px;
    height: 30px;
  }
}

.sidebarHeaderBar {
  display: flex;
  margin-bottom: 20px;
}

.sidebarBarButton {
  flex: 1;
  border: var(--border-in-light);
}

.sidebarBody {
  flex: 1;
  overflow: auto;
  margin-bottom: 20px;

  &::-webkit-scrollbar {
    display: none;
  }
}

.sidebarBodyTitle {
  font-size: 12px;
  font-weight: bold;
  color: var(--black);
  opacity: 0.7;
  margin-bottom: 10px;
  padding: 0 10px;
}

.discoveryList {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-bottom: 20px;
}

.discoveryItem {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-radius: 8px;
  color: var(--black);
  text-decoration: none;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--hover-color);
  }

  svg {
    width: 16px;
    height: 16px;
  }

  span {
    font-size: 14px;
  }
}

.sidebarTail {
  display: flex;
  justify-content: space-between;
  padding-top: 20px;
}

.sidebarActions {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.sidebarAction {
  a {
    color: inherit;
    text-decoration: none;
  }
}

.mobile {
  @media screen and (min-width: 600px) {
    display: none;
  }
}

@media screen and (max-width: 600px) {
  .sidebar {
    position: absolute;
    left: -100%;
    z-index: 1000;
    height: 100%;
    transition: left ease 0.3s;
    box-shadow: none;
    
    &.show {
      left: 0;
    }
  }
}
