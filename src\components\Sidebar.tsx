import { Link, useNavigate } from 'react-router-dom'
import { observer } from 'mobx-react-lite'
import { useStores } from '../stores'
import { IconButton } from './ui/IconButton'
import { ChatList } from './ChatList'
import styles from './Sidebar.module.scss'
import clsx from 'clsx'

// Import icons (these would need to be created or imported from the original project)
import ChatGptIcon from '../icons/bot.svg?react'
import MaskIcon from '../icons/mask.svg?react'
import PluginIcon from '../icons/plugin.svg?react'
import SettingsIcon from '../icons/settings.svg?react'
import GithubIcon from '../icons/github.svg?react'
import DeleteIcon from '../icons/delete.svg?react'
import SearchIcon from '../icons/search.svg?react'
import McpIcon from '../icons/mcp.svg?react'

interface SidebarProps {
  className?: string
}

export const Sidebar = observer(({ className }: SidebarProps) => {
  const navigate = useNavigate()
  const { chatStore, configStore } = useStores()
  
  const shouldNarrow = configStore.sidebarWidth < 280

  const handleDeleteCurrentChat = async () => {
    if (window.confirm('Delete current chat?')) {
      chatStore.deleteSession(chatStore.currentSessionIndex)
    }
  }

  return (
    <div 
      className={clsx(styles.sidebar, className, {
        [styles.narrowSidebar]: shouldNarrow,
      })}
      style={{ width: configStore.sidebarWidth }}
    >
      {/* Header */}
      <div className={clsx(styles.sidebarHeader, {
        [styles.sidebarHeaderNarrow]: shouldNarrow,
      })}>
        <div className={styles.sidebarTitleContainer}>
          <div className={styles.sidebarTitle}>NextChat</div>
          <div className={styles.sidebarSubTitle}>Build your own AI assistant.</div>
        </div>
        <div className={clsx(styles.sidebarLogo, 'no-dark')}>
          <ChatGptIcon />
        </div>
      </div>

      {/* Action Bar */}
      <div className={styles.sidebarHeaderBar}>
        <IconButton
          icon={<MaskIcon />}
          text={shouldNarrow ? undefined : 'New Chat'}
          className={styles.sidebarBarButton}
          onClick={() => navigate('/new-chat')}
        />
      </div>

      {/* Discovery */}
      <div className={styles.sidebarBody}>
        <div className={styles.sidebarBodyTitle}>Discover</div>
        <div className={styles.discoveryList}>
          <Link to="/plugins" className={styles.discoveryItem}>
            <PluginIcon />
            <span>Plugins</span>
          </Link>
          <Link to="/search-chat" className={styles.discoveryItem}>
            <SearchIcon />
            <span>Search Chat</span>
          </Link>
          <Link to="/mcp-market" className={styles.discoveryItem}>
            <McpIcon />
            <span>MCP Market</span>
          </Link>
        </div>
      </div>

      {/* Chat List */}
      <div className={styles.sidebarBody}>
        <ChatList narrow={shouldNarrow} />
      </div>

      {/* Footer Actions */}
      <div className={styles.sidebarTail}>
        <div className={styles.sidebarActions}>
          <div className={clsx(styles.sidebarAction, styles.mobile)}>
            <IconButton
              icon={<DeleteIcon />}
              onClick={handleDeleteCurrentChat}
            />
          </div>
          <div className={styles.sidebarAction}>
            <Link to="/settings">
              <IconButton
                icon={<SettingsIcon />}
                shadow
              />
            </Link>
          </div>
          <div className={styles.sidebarAction}>
            <a href="https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web" target="_blank" rel="noopener noreferrer">
              <IconButton
                icon={<GithubIcon />}
                shadow
              />
            </a>
          </div>
        </div>
      </div>
    </div>
  )
})
