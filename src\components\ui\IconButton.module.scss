.iconButton {
  background-color: var(--white);
  border: none;
  border-radius: 10px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  user-select: none;
  outline: none;
  color: var(--black);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  min-width: 38px;
  min-height: 38px;

  &:hover {
    background-color: var(--hover-color);
  }

  &:active {
    transform: scale(0.95);
  }

  &.bordered {
    border: var(--border-in-light);
  }

  &.shadow {
    box-shadow: var(--card-shadow);
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.5;
    
    &:hover {
      background-color: var(--white);
    }
    
    &:active {
      transform: none;
    }
  }
}

.iconButtonIcon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;

  svg {
    width: 100%;
    height: 100%;
  }
}

.iconButtonText {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
