import React from 'react'
import clsx from 'clsx'
import styles from './IconButton.module.scss'

interface IconButtonProps {
  onClick?: () => void
  icon?: React.ReactNode
  text?: string
  bordered?: boolean
  shadow?: boolean
  className?: string
  title?: string
  disabled?: boolean
  tabIndex?: number
  autoFocus?: boolean
}

export const IconButton: React.FC<IconButtonProps> = ({
  onClick,
  icon,
  text,
  bordered,
  shadow,
  className,
  title,
  disabled,
  tabIndex,
  autoFocus,
}) => {
  return (
    <button
      className={clsx(
        styles.iconButton,
        {
          [styles.bordered]: bordered,
          [styles.shadow]: shadow,
          [styles.disabled]: disabled,
        },
        className
      )}
      onClick={onClick}
      title={title}
      disabled={disabled}
      role="button"
      tabIndex={tabIndex}
      autoFocus={autoFocus}
    >
      {icon && <div className={styles.iconButtonIcon}>{icon}</div>}
      {text && <div className={styles.iconButtonText}>{text}</div>}
    </button>
  )
}
