.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: var(--black);
}

.loadingLogo {
  margin-bottom: 20px;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-in-light);
  border-top: 3px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loadingText {
  font-size: 14px;
  opacity: 0.7;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
