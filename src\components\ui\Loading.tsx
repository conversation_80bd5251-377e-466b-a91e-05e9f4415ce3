import React from 'react'
import styles from './Loading.module.scss'
import clsx from 'clsx'

interface LoadingProps {
  noLogo?: boolean
  className?: string
}

export const Loading: React.FC<LoadingProps> = ({ noLogo, className }) => {
  return (
    <div className={clsx(styles.loading, className)}>
      {!noLogo && (
        <div className={styles.loadingLogo}>
          <div className={styles.loadingSpinner} />
        </div>
      )}
      <div className={styles.loadingText}>Loading...</div>
    </div>
  )
}
