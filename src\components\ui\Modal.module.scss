.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modalContent {
  background-color: var(--white);
  border-radius: 10px;
  box-shadow: var(--shadow);
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  animation: slide-in-from-top 0.3s ease;
}

.modalContentSmall {
  width: 100%;
  max-width: 400px;
}

.modalContentMedium {
  width: 100%;
  max-width: 600px;
}

.modalContentLarge {
  width: 100%;
  max-width: 800px;
}

.modalHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: var(--border-in-light);
}

.modalTitle {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: var(--black);
}

.modalCloseButton {
  min-width: 32px;
  min-height: 32px;
  padding: 8px;
}

.modalBody {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

@media (max-width: 600px) {
  .modalOverlay {
    padding: 10px;
  }

  .modalContent {
    max-height: 95vh;
  }

  .modalContentSmall,
  .modalContentMedium,
  .modalContentLarge {
    max-width: 100%;
  }

  .modalHeader {
    padding: 15px;
  }

  .modalBody {
    padding: 15px;
  }
}
