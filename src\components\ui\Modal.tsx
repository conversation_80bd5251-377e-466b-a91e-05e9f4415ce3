import React, { useEffect } from 'react'
import { createPortal } from 'react-dom'
import { IconButton } from './IconButton'
import styles from './Modal.module.scss'
import clsx from 'clsx'

interface ModalProps {
  title?: string
  children: React.ReactNode
  isOpen: boolean
  onClose: () => void
  className?: string
  size?: 'small' | 'medium' | 'large'
}

const CloseIcon = () => <span>×</span>

export const Modal: React.FC<ModalProps> = ({
  title,
  children,
  isOpen,
  onClose,
  className,
  size = 'medium',
}) => {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isOpen])

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
    }

    return () => {
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  if (!isOpen) return null

  return createPortal(
    <div className={styles.modalOverlay} onClick={onClose}>
      <div
        className={clsx(
          styles.modalContent,
          styles[`modalContent${size.charAt(0).toUpperCase() + size.slice(1)}`],
          className
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {title && (
          <div className={styles.modalHeader}>
            <h2 className={styles.modalTitle}>{title}</h2>
            <IconButton
              icon={<CloseIcon />}
              onClick={onClose}
              className={styles.modalCloseButton}
            />
          </div>
        )}
        <div className={styles.modalBody}>
          {children}
        </div>
      </div>
    </div>,
    document.body
  )
}
