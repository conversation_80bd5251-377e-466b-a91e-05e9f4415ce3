// 环境配置
export const config = {
  // API 基础 URL
  apiBaseUrl: import.meta.env.VITE_API_BASE_URL || '/api',
  
  // 应用版本
  version: import.meta.env.VITE_APP_VERSION || '2.15.8',
  
  // 是否为开发环境
  isDev: import.meta.env.DEV,
  
  // 是否为生产环境
  isProd: import.meta.env.PROD,
  
  // 默认模型
  defaultModel: import.meta.env.VITE_DEFAULT_MODEL || 'gpt-3.5-turbo',
  
  // 最大消息长度
  maxMessageLength: parseInt(import.meta.env.VITE_MAX_MESSAGE_LENGTH || '4000'),
  
  // 最大会话数量
  maxSessions: parseInt(import.meta.env.VITE_MAX_SESSIONS || '100'),
  
  // 是否启用调试模式
  debug: import.meta.env.VITE_DEBUG === 'true',
  
  // GitHub 仓库地址
  githubUrl: 'https://github.com/ChatGPTNextWeb/ChatGPT-Next-Web',
  
  // 支持的语言
  supportedLanguages: ['en', 'zh-CN', 'zh-TW', 'ja', 'ko'],
  
  // 默认主题
  defaultTheme: 'auto' as const,
  
  // 侧边栏配置
  sidebar: {
    defaultWidth: 300,
    minWidth: 200,
    maxWidth: 500,
    narrowWidth: 100,
  },
  
  // 聊天配置
  chat: {
    maxHistoryLength: 50,
    defaultTemperature: 0.5,
    defaultMaxTokens: 2000,
    streamingEnabled: true,
  },
  
  // 存储配置
  storage: {
    prefix: 'nextchat_',
    version: '1.0',
  },
} as const

// 类型导出
export type Config = typeof config
