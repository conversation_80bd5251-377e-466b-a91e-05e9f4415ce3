// 路径常量
export enum Path {
  Home = '/',
  Chat = '/chat',
  Settings = '/settings',
  NewChat = '/new-chat',
  Plugins = '/plugins',
  SearchChat = '/search-chat',
  McpMarket = '/mcp-market',
}

// 存储键常量
export const STORAGE_KEYS = {
  CHAT_SESSIONS: 'chat-sessions',
  APP_CONFIG: 'app-config',
  PLUGINS: 'plugins',
  THEME: 'theme',
} as const

// 默认配置
export const DEFAULT_MODELS = [
  'gpt-3.5-turbo',
  'gpt-4',
  'gpt-4-turbo',
  'gpt-4o',
] as const

export const DEFAULT_SIDEBAR_WIDTH = 300
export const MIN_SIDEBAR_WIDTH = 200
export const MAX_SIDEBAR_WIDTH = 500

// API 端点
export const API_ENDPOINTS = {
  CHAT: '/api/chat',
  CHAT_STREAM: '/api/chat/stream',
  MODELS: '/api/models',
  PLUGINS: '/api/plugins',
  CONFIG: '/api/config',
} as const

// 主题常量
export const THEMES = ['auto', 'light', 'dark'] as const

// 语言常量
export const LANGUAGES = [
  { code: 'en', name: 'English' },
  { code: 'zh-CN', name: '简体中文' },
  { code: 'zh-TW', name: '繁體中文' },
  { code: 'ja', name: '日本語' },
  { code: 'ko', name: '한국어' },
] as const

// 错误消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error, please check your connection',
  API_ERROR: 'API error, please try again later',
  INVALID_INPUT: 'Invalid input, please check your input',
  UNKNOWN_ERROR: 'Unknown error occurred',
} as const

// 成功消息
export const SUCCESS_MESSAGES = {
  SAVED: 'Settings saved successfully',
  COPIED: 'Copied to clipboard',
  DELETED: 'Deleted successfully',
} as const
