import { useEffect, useCallback } from 'react'

export interface KeyboardShortcut {
  key: string
  ctrl?: boolean
  shift?: boolean
  alt?: boolean
  meta?: boolean
  callback: () => void
  description?: string
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[]) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    for (const shortcut of shortcuts) {
      const {
        key,
        ctrl = false,
        shift = false,
        alt = false,
        meta = false,
        callback,
      } = shortcut

      const keyMatch = event.key.toLowerCase() === key.toLowerCase()
      const ctrlMatch = event.ctrlKey === ctrl
      const shiftMatch = event.shiftKey === shift
      const altMatch = event.altKey === alt
      const metaMatch = event.metaKey === meta

      if (keyMatch && ctrlMatch && shiftMatch && altMatch && metaMatch) {
        event.preventDefault()
        callback()
        break
      }
    }
  }, [shortcuts])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])
}

// 预定义的快捷键
export const KEYBOARD_SHORTCUTS = {
  NEW_CHAT: { key: 'n', ctrl: true, description: 'Ctrl+N: 新建聊天' },
  CLEAR_CHAT: { key: 'l', ctrl: true, description: 'Ctrl+L: 清空聊天' },
  COPY_LAST: { key: 'c', ctrl: true, shift: true, description: 'Ctrl+Shift+C: 复制最后一条消息' },
  FOCUS_INPUT: { key: '/', description: '/: 聚焦输入框' },
  SEND_MESSAGE: { key: 'Enter', description: 'Enter: 发送消息' },
  NEW_LINE: { key: 'Enter', shift: true, description: 'Shift+Enter: 换行' },
  ESCAPE: { key: 'Escape', description: 'Esc: 取消/关闭' },
} as const
