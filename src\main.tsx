import React from 'react'
import ReactDOM from 'react-dom/client'
import { <PERSON><PERSON>er<PERSON>out<PERSON> } from 'react-router-dom'
import App from './App'
import './styles/globals.scss'
import './styles/markdown.scss'
import './styles/highlight.scss'

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
)
