import { makeAutoObservable, runInAction } from 'mobx'
import { nanoid } from 'nanoid'
import type { RootStore } from './index'

export interface ChatMessage {
  id: string
  role: 'user' | 'assistant' | 'system'
  content: string
  date: string
  streaming?: boolean
  isError?: boolean
  model?: string
  tools?: any[]
}

export interface ChatSession {
  id: string
  topic: string
  messages: ChatMessage[]
  stat: {
    tokenCount: number
    wordCount: number
    charCount: number
  }
  lastUpdate: number
  lastSummarizeIndex: number
  clearContextIndex?: number
}

export const createMessage = (override: Partial<ChatMessage>): ChatMessage => ({
  id: nanoid(),
  date: new Date().toLocaleString(),
  role: 'user',
  content: '',
  ...override,
})

export const createEmptySession = (): ChatSession => ({
  id: nanoid(),
  topic: 'New Chat',
  messages: [],
  stat: {
    tokenCount: 0,
    wordCount: 0,
    charCount: 0,
  },
  lastUpdate: Date.now(),
  lastSummarizeIndex: 0,
})

export class ChatStore {
  sessions: ChatSession[] = [createEmptySession()]
  currentSessionIndex = 0
  lastInput = ''

  constructor(private rootStore: RootStore) {
    makeAutoObservable(this)
    this.loadFromStorage()
  }

  get currentSession() {
    return this.sessions[this.currentSessionIndex] || this.sessions[0]
  }

  newSession = () => {
    const session = createEmptySession()
    runInAction(() => {
      this.sessions.push(session)
      this.currentSessionIndex = this.sessions.length - 1
    })
    this.saveToStorage()
    return session
  }

  deleteSession = (index: number) => {
    if (this.sessions.length === 1) return
    
    runInAction(() => {
      this.sessions.splice(index, 1)
      if (this.currentSessionIndex >= this.sessions.length) {
        this.currentSessionIndex = this.sessions.length - 1
      }
    })
    this.saveToStorage()
  }

  selectSession = (index: number) => {
    runInAction(() => {
      this.currentSessionIndex = Math.max(0, Math.min(index, this.sessions.length - 1))
    })
  }

  updateCurrentSession = (updater: (session: ChatSession) => void) => {
    const session = this.currentSession
    runInAction(() => {
      updater(session)
      session.lastUpdate = Date.now()
    })
    this.saveToStorage()
  }

  onUserInput = async (content: string, attachImages?: any[]) => {
    const userMessage = createMessage({
      role: 'user',
      content,
    })

    this.updateCurrentSession((session) => {
      session.messages.push(userMessage)
    })

    // 这里应该调用后端 API
    await this.getResponse(content)
  }

  private getResponse = async (userInput: string) => {
    const botMessage = createMessage({
      role: 'assistant',
      content: '',
      streaming: true,
    })

    this.updateCurrentSession((session) => {
      session.messages.push(botMessage)
    })

    try {
      // 使用新的 API
      const { chatApi } = await import('../api')

      const response = await chatApi.sendMessage({
        messages: this.currentSession.messages.slice(0, -1),
        model: this.rootStore.configStore.model,
        temperature: this.rootStore.configStore.config.modelConfig.temperature,
        max_tokens: this.rootStore.configStore.config.modelConfig.max_tokens,
      })

      runInAction(() => {
        botMessage.content = response.content || 'Sorry, I encountered an error.'
        botMessage.streaming = false
        botMessage.isError = false
      })
    } catch (error) {
      console.error('Chat API error:', error)
      runInAction(() => {
        botMessage.content = 'Sorry, I encountered an error while processing your request.'
        botMessage.streaming = false
        botMessage.isError = true
      })
    }

    this.saveToStorage()
  }

  // 删除消息
  deleteMessage = (messageId: string) => {
    runInAction(() => {
      const session = this.currentSession
      session.messages = session.messages.filter(m => m.id !== messageId)
    })
    this.saveToStorage()
  }

  // 编辑消息
  editMessage = (messageId: string, newContent: string) => {
    runInAction(() => {
      const session = this.currentSession
      const message = session.messages.find(m => m.id === messageId)
      if (message) {
        message.content = newContent
        message.date = new Date().toLocaleString()
      }
    })
    this.saveToStorage()
  }

  // 重新生成消息
  resendMessage = async (message: ChatMessage) => {
    const session = this.currentSession
    const messageIndex = session.messages.findIndex(m => m.id === message.id)

    if (messageIndex === -1) return

    // 如果是用户消息，重新发送
    if (message.role === 'user') {
      // 删除该消息及其后面的所有消息
      runInAction(() => {
        session.messages = session.messages.slice(0, messageIndex)
      })
      // 重新发送
      await this.onUserInput(message.content)
    } else if (message.role === 'assistant') {
      // 如果是助手消息，找到前面的用户消息重新发送
      const userMessageIndex = messageIndex - 1
      if (userMessageIndex >= 0 && session.messages[userMessageIndex].role === 'user') {
        const userMessage = session.messages[userMessageIndex]
        // 删除用户消息及其后面的所有消息
        runInAction(() => {
          session.messages = session.messages.slice(0, userMessageIndex)
        })
        // 重新发送
        await this.onUserInput(userMessage.content)
      }
    }
  }

  // 固定消息到上下文
  pinMessage = (message: ChatMessage) => {
    // 这里可以实现将消息固定到系统提示词或上下文中的逻辑
    console.log('Pin message:', message)
    // 暂时只是打印，后续可以扩展
  }

  private saveToStorage = () => {
    try {
      localStorage.setItem('chat-sessions', JSON.stringify({
        sessions: this.sessions,
        currentSessionIndex: this.currentSessionIndex,
        lastInput: this.lastInput,
      }))
    } catch (error) {
      console.error('Failed to save chat data:', error)
    }
  }

  private loadFromStorage = () => {
    try {
      const data = localStorage.getItem('chat-sessions')
      if (data) {
        const parsed = JSON.parse(data)
        runInAction(() => {
          this.sessions = parsed.sessions || [createEmptySession()]
          this.currentSessionIndex = parsed.currentSessionIndex || 0
          this.lastInput = parsed.lastInput || ''
        })
      }
    } catch (error) {
      console.error('Failed to load chat data:', error)
    }
  }
}
