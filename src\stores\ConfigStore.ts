import { makeAutoObservable, runInAction } from 'mobx'
import type { RootStore } from './index'

export interface ModelConfig {
  model: string
  temperature: number
  top_p: number
  max_tokens: number
  presence_penalty: number
  frequency_penalty: number
  sendMemory: boolean
  historyMessageCount: number
  compressMessageLengthThreshold: number
  enableInjectSystemPrompts: boolean
  template: string
}

export interface AppConfig {
  lastUpdate: number
  submitKey: 'Enter' | 'Ctrl+Enter' | 'Shift+Enter'
  avatar: string
  fontSize: number
  theme: 'auto' | 'light' | 'dark'
  tightBorder: boolean
  sendPreviewBubble: boolean
  enableAutoGenerateTitle: boolean
  sidebarWidth: number
  disablePromptHint: boolean
  dontShowMaskSplashScreen: boolean
  hideBuiltinMasks: boolean
  customModels: string
  modelConfig: ModelConfig
}

const DEFAULT_CONFIG: AppConfig = {
  lastUpdate: Date.now(),
  submitKey: 'Enter',
  avatar: '1f603',
  fontSize: 14,
  theme: 'auto',
  tightBorder: false,
  sendPreviewBubble: true,
  enableAutoGenerateTitle: true,
  sidebarWidth: 300,
  disablePromptHint: false,
  dontShowMaskSplashScreen: false,
  hideBuiltinMasks: false,
  customModels: '',
  modelConfig: {
    model: 'gpt-3.5-turbo',
    temperature: 0.5,
    top_p: 1,
    max_tokens: 2000,
    presence_penalty: 0,
    frequency_penalty: 0,
    sendMemory: true,
    historyMessageCount: 4,
    compressMessageLengthThreshold: 1000,
    enableInjectSystemPrompts: true,
    template: '',
  },
}

export class ConfigStore {
  config: AppConfig = { ...DEFAULT_CONFIG }

  constructor(private rootStore: RootStore) {
    makeAutoObservable(this)
    this.loadFromStorage()
  }

  get model() {
    return this.config.modelConfig.model
  }

  get theme() {
    return this.config.theme
  }

  get fontSize() {
    return this.config.fontSize
  }

  get sidebarWidth() {
    return this.config.sidebarWidth
  }

  update = (updater: (config: AppConfig) => void) => {
    runInAction(() => {
      updater(this.config)
      this.config.lastUpdate = Date.now()
    })
    this.saveToStorage()
  }

  updateModelConfig = (updater: (config: ModelConfig) => void) => {
    runInAction(() => {
      updater(this.config.modelConfig)
      this.config.lastUpdate = Date.now()
    })
    this.saveToStorage()
  }

  reset = () => {
    runInAction(() => {
      this.config = { ...DEFAULT_CONFIG }
    })
    this.saveToStorage()
  }

  private saveToStorage = () => {
    try {
      localStorage.setItem('app-config', JSON.stringify(this.config))
    } catch (error) {
      console.error('Failed to save config:', error)
    }
  }

  private loadFromStorage = () => {
    try {
      const data = localStorage.getItem('app-config')
      if (data) {
        const parsed = JSON.parse(data)
        runInAction(() => {
          this.config = { ...DEFAULT_CONFIG, ...parsed }
        })
      }
    } catch (error) {
      console.error('Failed to load config:', error)
    }
  }
}
