import { makeAutoObservable, runInAction } from 'mobx'
import { nanoid } from 'nanoid'
import type { RootStore } from './index'

export interface Plugin {
  id: string
  title: string
  version: string
  content: string
  builtin: boolean
  createdAt: number
}

export const createEmptyPlugin = (): Plugin => ({
  id: nanoid(),
  title: 'New Plugin',
  version: '1.0.0',
  content: '',
  builtin: false,
  createdAt: Date.now(),
})

export class PluginStore {
  plugins: Record<string, Plugin> = {}

  constructor(private rootStore: RootStore) {
    makeAutoObservable(this)
    this.loadFromStorage()
  }

  get allPlugins() {
    return Object.values(this.plugins)
  }

  get builtinPlugins() {
    return this.allPlugins.filter(p => p.builtin)
  }

  get customPlugins() {
    return this.allPlugins.filter(p => !p.builtin)
  }

  create = (plugin?: Partial<Plugin>) => {
    const newPlugin = {
      ...createEmptyPlugin(),
      ...plugin,
    }

    runInAction(() => {
      this.plugins[newPlugin.id] = newPlugin
    })
    
    this.saveToStorage()
    return newPlugin
  }

  update = (id: string, updater: (plugin: Plugin) => void) => {
    const plugin = this.plugins[id]
    if (!plugin) return

    runInAction(() => {
      updater(plugin)
    })
    
    this.saveToStorage()
  }

  delete = (id: string) => {
    runInAction(() => {
      delete this.plugins[id]
    })
    this.saveToStorage()
  }

  get = (id: string) => {
    return this.plugins[id]
  }

  private saveToStorage = () => {
    try {
      localStorage.setItem('plugins', JSON.stringify(this.plugins))
    } catch (error) {
      console.error('Failed to save plugins:', error)
    }
  }

  private loadFromStorage = () => {
    try {
      const data = localStorage.getItem('plugins')
      if (data) {
        const parsed = JSON.parse(data)
        runInAction(() => {
          this.plugins = parsed || {}
        })
      }
    } catch (error) {
      console.error('Failed to load plugins:', error)
    }
  }
}
