import { createContext, useContext } from 'react'
import { ChatStore } from './ChatStore'
import { ConfigStore } from './ConfigStore'
import { PluginStore } from './PluginStore'

export class RootStore {
  chatStore: ChatStore
  configStore: ConfigStore
  pluginStore: PluginStore

  constructor() {
    this.chatStore = new ChatStore(this)
    this.configStore = new ConfigStore(this)
    this.pluginStore = new PluginStore(this)
  }
}

const rootStore = new RootStore()
const StoreContext = createContext(rootStore)

export const useStores = () => {
  const context = useContext(StoreContext)
  if (!context) {
    throw new Error('useStores must be used within a StoreProvider')
  }
  return context
}

export { StoreContext }
export * from './ChatStore'
export * from './ConfigStore'
export * from './PluginStore'
