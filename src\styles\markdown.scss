/* Markdown styles */
.markdown-body {
  line-height: 1.6;
  
  h1, h2, h3, h4, h5, h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
  }

  h1 {
    font-size: 2em;
    border-bottom: 1px solid var(--border-in-light);
    padding-bottom: 0.3em;
  }

  h2 {
    font-size: 1.5em;
    border-bottom: 1px solid var(--border-in-light);
    padding-bottom: 0.3em;
  }

  p {
    margin-bottom: 16px;
  }

  code {
    padding: 0.2em 0.4em;
    margin: 0;
    font-size: 85%;
    background-color: var(--hover-color);
    border-radius: 6px;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  }

  pre {
    padding: 16px;
    overflow: auto;
    font-size: 85%;
    line-height: 1.45;
    background-color: var(--hover-color);
    border-radius: 6px;
    margin-bottom: 16px;

    code {
      background-color: transparent;
      padding: 0;
      margin: 0;
      border-radius: 0;
    }
  }

  blockquote {
    padding: 0 1em;
    color: var(--black);
    opacity: 0.7;
    border-left: 0.25em solid var(--border-in-light);
    margin: 0 0 16px 0;
  }

  ul, ol {
    padding-left: 2em;
    margin-bottom: 16px;
  }

  li {
    margin-bottom: 0.25em;
  }

  table {
    border-collapse: collapse;
    margin-bottom: 16px;
    width: 100%;
  }

  th, td {
    padding: 6px 13px;
    border: 1px solid var(--border-in-light);
  }

  th {
    font-weight: 600;
    background-color: var(--hover-color);
  }
}
