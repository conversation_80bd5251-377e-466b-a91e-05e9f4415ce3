@mixin light {
  --theme: light;

  /* color */
  --white: white;
  --black: rgb(48, 48, 48);
  --gray: rgb(250, 250, 250);
  --primary: rgb(29, 147, 171);
  --second: rgb(231, 248, 255);
  --hover-color: #f3f3f3;
  --bar-color: rgba(0, 0, 0, 0.1);
  --theme-color: var(--gray);

  /* shadow */
  --shadow: 50px 50px 100px 10px rgb(0, 0, 0, 0.1);
  --card-shadow: 0px 2px 4px 0px rgb(0, 0, 0, 0.05);

  /* stroke */
  --border-in-light: 1px solid rgb(222, 222, 222);
}

@mixin dark {
  --theme: dark;

  /* color */
  --white: rgb(30, 30, 30);
  --black: rgb(187, 187, 187);
  --gray: rgb(21, 21, 21);
  --primary: rgb(29, 147, 171);
  --second: rgb(27 38 42);
  --hover-color: #323232;

  --bar-color: rgba(255, 255, 255, 0.1);

  --border-in-light: 1px solid rgba(255, 255, 255, 0.192);

  --theme-color: var(--gray);

  div:not(.no-dark) > svg {
    filter: invert(0.5);
  }
}
